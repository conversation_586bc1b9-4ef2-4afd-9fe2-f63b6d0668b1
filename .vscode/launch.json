{"version": "0.2.0", "configurations": [{"name": "Debug with LLDB", "type": "lldb", "request": "launch", "program": "${workspaceFolder}/bazel-bin/${input:targetName}", "args": [], "cwd": "${workspaceFolder}", "preLaunchTask": "build", "console": "integratedTerminal", "stopOnEntry": false, "environment": []}, {"name": "Attach to Process", "type": "lldb", "request": "attach", "pid": "${command:pickProcess}"}], "inputs": [{"id": "targetName", "description": "Target name to debug", "default": "main", "type": "promptString"}]}