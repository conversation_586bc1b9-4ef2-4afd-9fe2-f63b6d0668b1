{"version": "2.0.0", "tasks": [{"label": "build", "type": "shell", "command": "bazel", "args": ["build", "//..."], "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "problemMatcher": {"owner": "cpp", "fileLocation": ["relative", "${workspaceFolder}"], "pattern": {"regexp": "^(.*):(\\d+):(\\d+):\\s+(warning|error):\\s+(.*)$", "file": 1, "line": 2, "column": 3, "severity": 4, "message": 5}}}, {"label": "test", "type": "shell", "command": "bazel", "args": ["test", "//..."], "group": {"kind": "test", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "clean", "type": "shell", "command": "bazel", "args": ["clean"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}, {"label": "format", "type": "shell", "command": "find", "args": [".", "-name", "*.cpp", "-o", "-name", "*.hpp", "-o", "-name", "*.h", "-o", "-name", "*.cc", "|", "xargs", "clang-format", "-i"], "group": "build", "presentation": {"echo": true, "reveal": "silent", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}}]}