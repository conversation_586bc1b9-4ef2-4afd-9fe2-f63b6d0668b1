{
  // C++ Language Server - Use clangd instead of C++ extension
  "C_Cpp.intelliSenseEngine": "disabled",
  "clangd.path": "clangd",
  "clangd.arguments": [
    "--background-index",
    "--clang-tidy",
    "--completion-style=detailed",
    "--function-arg-placeholders",
    "--header-insertion=iwyu",
    "--pch-storage=memory",
    "--pretty",
    "--ranking-model=decision_forest"
  ],
  "clangd.fallbackFlags": [
    "-std=c++20"
  ],
  
  // Formatting
  "editor.formatOnSave": true,
  "editor.formatOnType": true,
  "editor.formatOnPaste": true,
  "C_Cpp.formatting": "disabled",
  "editor.defaultFormatter": "llvm-vs-code-extensions.vscode-clangd",
  
  // File associations
  "files.associations": {
    "*.h": "cpp",
    "*.hpp": "cpp",
    "*.cpp": "cpp",
    "*.cc": "cpp",
    "*.cxx": "cpp",
    "BUILD": "starlark",
    "BUILD.bazel": "starlark",
    "WORKSPACE": "starlark",
    "*.bzl": "starlark",
    ".bazelrc": "shellscript"
  },
  
  // Bazel integration
  "bazel.buildifierFixOnFormat": true,
  "bazel.enableCodeLens": true,
  
  // Editor settings
  "editor.tabSize": 2,
  "editor.insertSpaces": true,
  "editor.detectIndentation": false,
  "editor.rulers": [80, 100],
  "editor.wordWrap": "wordWrapColumn",
  "editor.wordWrapColumn": 100,
  
  // File explorer
  "files.exclude": {
    "**/bazel-*": true,
    "**/.git": true,
    "**/node_modules": true,
    "**/vcpkg_installed": true
  },
  
  // Search
  "search.exclude": {
    "**/bazel-*": true,
    "**/vcpkg_installed": true
  },
  
  // Terminal
  "terminal.integrated.env.osx": {
    "BAZEL_COMPLETION": "1"
  },
  "terminal.integrated.env.linux": {
    "BAZEL_COMPLETION": "1"
  }
}
