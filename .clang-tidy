---
Checks: >
  *,
  -abseil-*,
  -altera-*,
  -android-*,
  -fuchsia-*,
  -google-*,
  -llvm*,
  -zircon-*,
  -readability-else-after-return,
  -readability-static-accessed-through-instance,
  -readability-avoid-const-params-in-decls,
  -cppcoreguidelines-non-private-member-variables-in-classes,
  -misc-non-private-member-variables-in-classes,
  -modernize-use-trailing-return-type,
  -modernize-avoid-c-arrays,
  -cppcoreguidelines-avoid-c-arrays,
  -hicpp-avoid-c-arrays,
  -cppcoreguidelines-pro-bounds-constant-array-index,
  -cppcoreguidelines-pro-bounds-array-to-pointer-decay,
  -hicpp-no-array-decay,
  -cppcoreguidelines-avoid-magic-numbers,
  -readability-magic-numbers,
  -cert-err58-cpp,
  -hicpp-special-member-functions,
  -cppcoreguidelines-special-member-functions,
  -readability-named-parameter,
  -misc-unused-parameters,
  -bugprone-easily-swappable-parameters

WarningsAsErrors: ''
HeaderFilterRegex: '(src/.*|include/.*|tests/.*)'
AnalyzeTemporaryDtors: false
FormatStyle: 'file'
CheckOptions:
  - key: readability-identifier-naming.NamespaceCase
    value: lower_case
  - key: readability-identifier-naming.ClassCase
    value: CamelCase
  - key: readability-identifier-naming.StructCase
    value: CamelCase
  - key: readability-identifier-naming.TemplateParameterCase
    value: CamelCase
  - key: readability-identifier-naming.FunctionCase
    value: camelBack
  - key: readability-identifier-naming.VariableCase
    value: lower_case
  - key: readability-identifier-naming.ClassMemberCase
    value: lower_case
  - key: readability-identifier-naming.ClassMemberSuffix
    value: _
  - key: readability-identifier-naming.PrivateMemberSuffix
    value: _
  - key: readability-identifier-naming.ProtectedMemberSuffix
    value: _
  - key: readability-identifier-naming.EnumConstantCase
    value: CamelCase
  - key: readability-identifier-naming.ConstantCase
    value: UPPER_CASE
  - key: readability-identifier-naming.StaticConstantCase
    value: UPPER_CASE
  - key: readability-identifier-naming.GlobalConstantCase
    value: UPPER_CASE
  - key: readability-identifier-naming.MemberConstantCase
    value: UPPER_CASE
  - key: readability-identifier-naming.StaticVariableCase
    value: lower_case
  - key: modernize-loop-convert.MaxCopySize
    value: 16
  - key: modernize-loop-convert.MinConfidence
    value: reasonable
  - key: modernize-loop-convert.NamingStyle
    value: CamelCase
  - key: modernize-pass-by-value.IncludeStyle
    value: llvm
  - key: modernize-replace-auto-ptr.IncludeStyle
    value: llvm
  - key: modernize-use-nullptr.NullMacros
    value: 'NULL'
