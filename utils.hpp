#pragma once

#include <cstddef>
#include <iostream>
#include <limits>
#include <type_traits>
#include <typeinfo>

template <typename T>
T Max(T a, T b) {
  return a > b ? a : b;
}

template <typename T1, typename T2>
auto Product(T1 a, T2 b) -> decltype(a * b) {
  return a * b;
}

template <typename T1, typename T2>
std::common_type_t<T1, T2> Product2(T1 a, T2 b) {
  return a * b;
}

template <typename T1, typename T2, typename ResultT = std::common_type_t<T1, T2>>
ResultT Product3(T1 a, T2 b) {
  return a * b;
}

template <typename T>
void foo(T a) {
  std::cout << typeid(T).name() << std::endl;
}

template <auto value>
void Print() {
  std::cout << value << std::endl;
}

template <typename T, size_t size>
size_t find(T (&arr)[size], const T& value) {
  for (size_t i = 0; i < size; ++i) {
    if (arr[i] == value) {
      return i;
    }
  }
  return std::numeric_limits<size_t>::max();
}

// Non-type template class examples
template <int N>
class FixedArray {
private:
  int data_[N];

public:
  // Constructor declaration and definition inside class
  FixedArray() {
    for (int i = 0; i < N; ++i) {
      data_[i] = 0;
    }
  }

  // Function declaration inside class
  void fill(int value);
  int& at(int index);
  const int& at(int index) const;
  constexpr int size() const { return N; }

  // Template member function
  template <typename T>
  void print(T prefix) const;
};

// Function definition outside class
template <int N>
void FixedArray<N>::fill(int value) {
  for (int i = 0; i < N; ++i) {
    data_[i] = value;
  }
}

template <int N>
int& FixedArray<N>::at(int index) {
  if (index < 0 || index >= N) {
    throw std::out_of_range("Index out of bounds");
  }
  return data_[index];
}

template <int N>
const int& FixedArray<N>::at(int index) const {
  if (index < 0 || index >= N) {
    throw std::out_of_range("Index out of bounds");
  }
  return data_[index];
}

// Template member function definition outside class
template <int N>
template <typename T>
void FixedArray<N>::print(T prefix) const {
  std::cout << prefix << ": [";
  for (int i = 0; i < N; ++i) {
    std::cout << data_[i];
    if (i < N - 1) std::cout << ", ";
  }
  std::cout << "]" << std::endl;
}

// Multiple non-type template parameters
template <typename T, size_t Rows, size_t Cols>
class Matrix {
private:
  T data_[Rows][Cols];

public:
  // Constructor with initializer list
  Matrix(std::initializer_list<std::initializer_list<T>> init);

  // Inline function definitions
  constexpr size_t rows() const { return Rows; }
  constexpr size_t cols() const { return Cols; }
  constexpr size_t size() const { return Rows * Cols; }

  // Function declarations
  T& operator()(size_t row, size_t col);
  const T& operator()(size_t row, size_t col) const;
  void fill(const T& value);

  // Static member function
  static constexpr bool is_square() { return Rows == Cols; }
};

// Constructor definition outside class
template <typename T, size_t Rows, size_t Cols>
Matrix<T, Rows, Cols>::Matrix(std::initializer_list<std::initializer_list<T>> init) {
  size_t row = 0;
  for (const auto& row_data : init) {
    if (row >= Rows) break;
    size_t col = 0;
    for (const auto& value : row_data) {
      if (col >= Cols) break;
      data_[row][col] = value;
      ++col;
    }
    ++row;
  }
}

// Operator definitions outside class
template <typename T, size_t Rows, size_t Cols>
T& Matrix<T, Rows, Cols>::operator()(size_t row, size_t col) {
  if (row >= Rows || col >= Cols) {
    throw std::out_of_range("Matrix index out of bounds");
  }
  return data_[row][col];
}

template <typename T, size_t Rows, size_t Cols>
const T& Matrix<T, Rows, Cols>::operator()(size_t row, size_t col) const {
  if (row >= Rows || col >= Cols) {
    throw std::out_of_range("Matrix index out of bounds");
  }
  return data_[row][col];
}

template <typename T, size_t Rows, size_t Cols>
void Matrix<T, Rows, Cols>::fill(const T& value) {
  for (size_t i = 0; i < Rows; ++i) {
    for (size_t j = 0; j < Cols; ++j) {
      data_[i][j] = value;
    }
  }
}

// C++17/20 auto non-type template parameter
template <auto Value>
class Constant {
public:
  using value_type = decltype(Value);

  // Inline constexpr function
  static constexpr auto get() { return Value; }

  // Function declarations
  static void print();
  template <typename T>
  static constexpr bool equals(T other);

  // Constexpr if example
  static void describe();
};

// Function definitions outside class
template <auto Value>
void Constant<Value>::print() {
  std::cout << "Constant value: " << Value << std::endl;
}

template <auto Value>
template <typename T>
constexpr bool Constant<Value>::equals(T other) {
  return Value == other;
}

template <auto Value>
void Constant<Value>::describe() {
  std::cout << "Type: " << typeid(decltype(Value)).name() << ", Value: " << Value;

  if constexpr (std::is_integral_v<decltype(Value)>) {
    std::cout << " (integer)";
  } else if constexpr (std::is_floating_point_v<decltype(Value)>) {
    std::cout << " (floating point)";
  } else {
    std::cout << " (other type)";
  }
  std::cout << std::endl;
}