#pragma once

#include <algorithm>
#include <array>
#include <cstddef>
#include <iostream>
#include <limits>
#include <numeric>
#include <stdexcept>
#include <type_traits>
#include <typeinfo>
#include <vector>

#ifdef __ARM_NEON
#include <arm_neon.h>
#endif

#ifdef __SSE__
#include <immintrin.h>
#endif

template <typename T>
T Max(T a, T b) {
  return a > b ? a : b;
}

template <typename T1, typename T2>
auto Product(T1 a, T2 b) -> decltype(a * b) {
  return a * b;
}

template <typename T1, typename T2>
std::common_type_t<T1, T2> Product2(T1 a, T2 b) {
  return a * b;
}

template <typename T1, typename T2, typename ResultT = std::common_type_t<T1, T2>>
ResultT Product3(T1 a, T2 b) {
  return a * b;
}

template <typename T>
void foo(T a) {
  std::cout << typeid(T).name() << std::endl;
}

template <auto value>
void Print() {
  std::cout << value << std::endl;
}

template <typename T, size_t size>
size_t find(T (&arr)[size], const T& value) {
  for (size_t i = 0; i < size; ++i) {
    if (arr[i] == value) {
      return i;
    }
  }
  return std::numeric_limits<size_t>::max();
}

// Non-type template class examples
template <int N>
class FixedArray {
private:
  int data_[N];

public:
  // Constructor declaration and definition inside class
  FixedArray() {
    for (int i = 0; i < N; ++i) {
      data_[i] = 0;
    }
  }

  // Function declaration inside class
  void fill(int value);
  int& at(int index);
  const int& at(int index) const;
  constexpr int size() const { return N; }

  // Template member function
  template <typename T>
  void print(T prefix) const;
};

// Function definition outside class
template <int N>
void FixedArray<N>::fill(int value) {
  for (int i = 0; i < N; ++i) {
    data_[i] = value;
  }
}

template <int N>
int& FixedArray<N>::at(int index) {
  if (index < 0 || index >= N) {
    throw std::out_of_range("Index out of bounds");
  }
  return data_[index];
}

template <int N>
const int& FixedArray<N>::at(int index) const {
  if (index < 0 || index >= N) {
    throw std::out_of_range("Index out of bounds");
  }
  return data_[index];
}

// Template member function definition outside class
template <int N>
template <typename T>
void FixedArray<N>::print(T prefix) const {
  std::cout << prefix << ": [";
  for (int i = 0; i < N; ++i) {
    std::cout << data_[i];
    if (i < N - 1) std::cout << ", ";
  }
  std::cout << "]" << std::endl;
}

// Multiple non-type template parameters
template <typename T, size_t Rows, size_t Cols>
class Matrix {
private:
  T data_[Rows][Cols];

public:
  // Constructor with initializer list
  Matrix(std::initializer_list<std::initializer_list<T>> init);

  // Inline function definitions
  constexpr size_t rows() const { return Rows; }
  constexpr size_t cols() const { return Cols; }
  constexpr size_t size() const { return Rows * Cols; }

  // Function declarations
  T& operator()(size_t row, size_t col);
  const T& operator()(size_t row, size_t col) const;
  void fill(const T& value);

  // Static member function
  static constexpr bool is_square() { return Rows == Cols; }
};

// Constructor definition outside class
template <typename T, size_t Rows, size_t Cols>
Matrix<T, Rows, Cols>::Matrix(std::initializer_list<std::initializer_list<T>> init) {
  size_t row = 0;
  for (const auto& row_data : init) {
    if (row >= Rows) break;
    size_t col = 0;
    for (const auto& value : row_data) {
      if (col >= Cols) break;
      data_[row][col] = value;
      ++col;
    }
    ++row;
  }
}

// Operator definitions outside class
template <typename T, size_t Rows, size_t Cols>
T& Matrix<T, Rows, Cols>::operator()(size_t row, size_t col) {
  if (row >= Rows || col >= Cols) {
    throw std::out_of_range("Matrix index out of bounds");
  }
  return data_[row][col];
}

template <typename T, size_t Rows, size_t Cols>
const T& Matrix<T, Rows, Cols>::operator()(size_t row, size_t col) const {
  if (row >= Rows || col >= Cols) {
    throw std::out_of_range("Matrix index out of bounds");
  }
  return data_[row][col];
}

template <typename T, size_t Rows, size_t Cols>
void Matrix<T, Rows, Cols>::fill(const T& value) {
  for (size_t i = 0; i < Rows; ++i) {
    for (size_t j = 0; j < Cols; ++j) {
      data_[i][j] = value;
    }
  }
}

// C++17/20 auto non-type template parameter
template <auto Value>
class Constant {
public:
  using value_type = decltype(Value);

  // Inline constexpr function
  static constexpr auto get() { return Value; }

  // Function declarations
  static void print();
  template <typename T>
  static constexpr bool equals(T other);

  // Constexpr if example
  static void describe();
};

// Function definitions outside class
template <auto Value>
void Constant<Value>::print() {
  std::cout << "Constant value: " << Value << std::endl;
}

template <auto Value>
template <typename T>
constexpr bool Constant<Value>::equals(T other) {
  return Value == other;
}

template <auto Value>
void Constant<Value>::describe() {
  std::cout << "Type: " << typeid(decltype(Value)).name() << ", Value: " << Value;

  if constexpr (std::is_integral_v<decltype(Value)>) {
    std::cout << " (integer)";
  } else if constexpr (std::is_floating_point_v<decltype(Value)>) {
    std::cout << " (floating point)";
  } else {
    std::cout << " (other type)";
  }
  std::cout << std::endl;
}

// ============================================================================
// OPTIMIZED WEIGHTED SUM IMPLEMENTATIONS FOR 9-ELEMENT FLOAT32 VECTORS
// ============================================================================

// Method 1: Simple loop (baseline)
inline float weighted_sum_simple(const std::vector<float>& alpha,
                                 const std::vector<float>& weight) {
  float sum = 0.0f;
  for (size_t i = 0; i < 9; ++i) {
    sum += alpha[i] * weight[i];
  }
  return sum;
}

// Method 2: Array version (faster due to known size)
inline float weighted_sum_array(const std::array<float, 9>& alpha,
                                const std::array<float, 9>& weight) {
  float sum = 0.0f;
  for (size_t i = 0; i < 9; ++i) {
    sum += alpha[i] * weight[i];
  }
  return sum;
}

// Method 3: Unrolled loop (fastest scalar version)
inline float weighted_sum_unrolled(const float* alpha, const float* weight) {
  return alpha[0] * weight[0] + alpha[1] * weight[1] + alpha[2] * weight[2] + alpha[3] * weight[3] +
         alpha[4] * weight[4] + alpha[5] * weight[5] + alpha[6] * weight[6] + alpha[7] * weight[7] +
         alpha[8] * weight[8];
}

// Method 4: STL inner_product
inline float weighted_sum_stl(const std::vector<float>& alpha, const std::vector<float>& weight) {
  return std::inner_product(alpha.begin(), alpha.begin() + 9, weight.begin(), 0.0f);
}

// Method 5: SIMD optimized (SSE/AVX)
#ifdef __SSE__
inline float weighted_sum_simd_sse(const float* alpha, const float* weight) {
  // Load first 8 elements (2 x 4-element vectors)
  __m128 a1 = _mm_loadu_ps(&alpha[0]);   // alpha[0-3]
  __m128 w1 = _mm_loadu_ps(&weight[0]);  // weight[0-3]
  __m128 a2 = _mm_loadu_ps(&alpha[4]);   // alpha[4-7]
  __m128 w2 = _mm_loadu_ps(&weight[4]);  // weight[4-7]

  // Multiply
  __m128 prod1 = _mm_mul_ps(a1, w1);
  __m128 prod2 = _mm_mul_ps(a2, w2);

  // Add the vectors
  __m128 sum_vec = _mm_add_ps(prod1, prod2);

  // Horizontal sum of the 4 elements
  __m128 shuf = _mm_movehdup_ps(sum_vec);
  __m128 sums = _mm_add_ps(sum_vec, shuf);
  shuf = _mm_movehl_ps(shuf, sums);
  sums = _mm_add_ss(sums, shuf);

  float result = _mm_cvtss_f32(sums);

  // Add the 9th element
  result += alpha[8] * weight[8];

  return result;
}
#endif

// Method 6: ARM NEON optimized
#ifdef __ARM_NEON
inline float weighted_sum_simd_neon(const float* alpha, const float* weight) {
  // Load first 8 elements (2 x 4-element vectors)
  float32x4_t a1 = vld1q_f32(&alpha[0]);   // alpha[0-3]
  float32x4_t w1 = vld1q_f32(&weight[0]);  // weight[0-3]
  float32x4_t a2 = vld1q_f32(&alpha[4]);   // alpha[4-7]
  float32x4_t w2 = vld1q_f32(&weight[4]);  // weight[4-7]

  // Multiply and accumulate
  float32x4_t prod1 = vmulq_f32(a1, w1);
  float32x4_t prod2 = vmlaq_f32(prod1, a2, w2);  // prod1 + a2 * w2

  // Horizontal sum
  float32x2_t sum_pair = vadd_f32(vget_low_f32(prod2), vget_high_f32(prod2));
  float32x2_t sum_final = vpadd_f32(sum_pair, sum_pair);

  float result = vget_lane_f32(sum_final, 0);

  // Add the 9th element
  result += alpha[8] * weight[8];

  return result;
}
#endif

// Method 7: Template version with compile-time optimization
template <size_t N>
inline float weighted_sum_template(const std::array<float, N>& alpha,
                                   const std::array<float, N>& weight) {
  float sum = 0.0f;
  for (size_t i = 0; i < N; ++i) {
    sum += alpha[i] * weight[i];
  }
  return sum;
}

// Method 8: Constexpr unrolled template (C++14+)
template <size_t I>
constexpr float weighted_sum_recursive_impl(const std::array<float, 9>& alpha,
                                            const std::array<float, 9>& weight) {
  if constexpr (I == 0) {
    return alpha[0] * weight[0];
  } else {
    return alpha[I] * weight[I] + weighted_sum_recursive_impl<I - 1>(alpha, weight);
  }
}

template <size_t N = 9>
constexpr float weighted_sum_constexpr(const std::array<float, N>& alpha,
                                       const std::array<float, N>& weight) {
  return weighted_sum_recursive_impl<N - 1>(alpha, weight);
}

// ============================================================================
// NON-TYPE TEMPLATE CLASS WITH PRIVATE WEIGHTED SUM MEMBER FUNCTION
// ============================================================================

template <size_t N>
class WeightedCalculator {
private:
  std::array<float, N> alpha_;
  std::array<float, N> weight_;

  // Private recursive implementation (Method 8 as member function)
  template <size_t I>
  constexpr float weighted_sum_impl() const {
    if constexpr (I == 0) {
      return alpha_[0] * weight_[0];
    } else {
      return alpha_[I] * weight_[I] + weighted_sum_impl<I - 1>();
    }
  }

  // Alternative: Private function with external arrays
  template <size_t I>
  static constexpr float weighted_sum_impl(const std::array<float, N>& alpha,
                                           const std::array<float, N>& weight) {
    if constexpr (I == 0) {
      return alpha[0] * weight[0];
    } else {
      return alpha[I] * weight[I] + weighted_sum_impl<I - 1>(alpha, weight);
    }
  }

public:
  // Constructor
  WeightedCalculator(const std::array<float, N>& alpha, const std::array<float, N>& weight)
      : alpha_(alpha), weight_(weight) {}

  // Public interface using private member function
  constexpr float calculate() const { return weighted_sum_impl<N - 1>(); }

  // Static public interface using private static function
  static constexpr float calculate(const std::array<float, N>& alpha,
                                   const std::array<float, N>& weight) {
    return weighted_sum_impl<N - 1>(alpha, weight);
  }

  // Getters
  const std::array<float, N>& get_alpha() const { return alpha_; }
  const std::array<float, N>& get_weight() const { return weight_; }

  // Setters
  void set_alpha(const std::array<float, N>& alpha) { alpha_ = alpha; }
  void set_weight(const std::array<float, N>& weight) { weight_ = weight; }

  // Compile-time size
  static constexpr size_t size() { return N; }
};

// Specialized version for 9-element vectors (your use case)
using WeightedCalculator9 = WeightedCalculator<9>;

// Advanced example: Template class with multiple private weighted sum methods
template <size_t N, typename T = float>
class AdvancedWeightedCalculator {
private:
  std::array<T, N> data1_;
  std::array<T, N> data2_;

  // Private Method 1: Recursive constexpr (compile-time optimized)
  template <size_t I>
  constexpr T weighted_sum_recursive() const {
    if constexpr (I == 0) {
      return data1_[0] * data2_[0];
    } else {
      return data1_[I] * data2_[I] + weighted_sum_recursive<I - 1>();
    }
  }

  // Private Method 2: Unrolled loop (runtime optimized)
  T weighted_sum_unrolled() const {
    if constexpr (N == 9) {
      return data1_[0] * data2_[0] + data1_[1] * data2_[1] + data1_[2] * data2_[2] +
             data1_[3] * data2_[3] + data1_[4] * data2_[4] + data1_[5] * data2_[5] +
             data1_[6] * data2_[6] + data1_[7] * data2_[7] + data1_[8] * data2_[8];
    } else {
      T sum = T{0};
      for (size_t i = 0; i < N; ++i) {
        sum += data1_[i] * data2_[i];
      }
      return sum;
    }
  }

  // Private Method 3: SIMD optimized (for float specialization)
  template <typename U = T>
  typename std::enable_if_t<std::is_same_v<U, float> && N >= 8, float> weighted_sum_simd() const {
#ifdef __SSE__
    if constexpr (N >= 8) {
      // Process 8 elements with SIMD, handle remainder
      __m128 a1 = _mm_loadu_ps(&data1_[0]);
      __m128 w1 = _mm_loadu_ps(&data2_[0]);
      __m128 a2 = _mm_loadu_ps(&data1_[4]);
      __m128 w2 = _mm_loadu_ps(&data2_[4]);

      __m128 prod1 = _mm_mul_ps(a1, w1);
      __m128 prod2 = _mm_mul_ps(a2, w2);
      __m128 sum_vec = _mm_add_ps(prod1, prod2);

      // Horizontal sum
      __m128 shuf = _mm_movehdup_ps(sum_vec);
      __m128 sums = _mm_add_ps(sum_vec, shuf);
      shuf = _mm_movehl_ps(shuf, sums);
      sums = _mm_add_ss(sums, shuf);

      float result = _mm_cvtss_f32(sums);

      // Handle remaining elements
      for (size_t i = 8; i < N; ++i) {
        result += data1_[i] * data2_[i];
      }
      return result;
    }
#endif
    return weighted_sum_unrolled();
  }

public:
  // Constructor
  AdvancedWeightedCalculator(const std::array<T, N>& data1, const std::array<T, N>& data2)
      : data1_(data1), data2_(data2) {}

  // Public interfaces to private methods
  constexpr T calculate_constexpr() const { return weighted_sum_recursive<N - 1>(); }

  T calculate_fast() const { return weighted_sum_unrolled(); }

  template <typename U = T>
  typename std::enable_if_t<std::is_same_v<U, float>, float> calculate_simd() const {
    return weighted_sum_simd();
  }
};